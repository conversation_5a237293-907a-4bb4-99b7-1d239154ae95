import { NextResponse } from 'next/server';
import { supabase as supabaseServerClient } from '@/lib/supabaseClient';

export const GET = async () => {
    try {
        const { data, error } = await supabaseServerClient
            .from('recipes')
            .select('*')
            .order('id', { ascending: true });
        
        if (error) {
            throw error;
        }

        return NextResponse.json(data, { status: 200 });
    } catch (error: any) {
        console.error('Error fetching recipes:', error.message);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
};

export const POST = async (req: Request) => {
    try {
        const body = await req.json();
        const { name, quantity, ingredients, type, stock } = body;

        if (!name || quantity === undefined || !type || stock === undefined) {
            return NextResponse.json({ 
                error: 'Name, quantity, type, and stock are required' 
            }, { status: 400 });
        }

        // Validate ingredients array structure
        if (ingredients && !Array.isArray(ingredients)) {
            return NextResponse.json({ 
                error: 'Ingredients must be an array' 
            }, { status: 400 });
        }

        // Validate each ingredient in the array
        if (ingredients) {
            for (const ingredient of ingredients) {
                if (!ingredient.ingredient_id || ingredient.deduct_amount === undefined || ingredient.deduct_stock === undefined) {
                    return NextResponse.json({ 
                        error: 'Each ingredient must have ingredient_id, deduct_amount, and deduct_stock' 
                    }, { status: 400 });
                }
            }
        }

        const { data, error } = await supabaseServerClient
            .from('recipes')
            .insert({
                name,
                quantity,
                ingredients: ingredients || [],
                type,
                stock
            })
            .select();

        if (error) {
            throw error;
        }

        return NextResponse.json(data[0], { status: 201 });
    } catch (error: any) {
        console.error('Error creating recipe:', error.message);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
};

export const PUT = async (req: Request) => {
    try {
        const body = await req.json();
        const { id, name, quantity, ingredients, type, stock } = body;

        if (!id) {
            return NextResponse.json({ error: 'Recipe ID is required' }, { status: 400 });
        }

        const updateData: any = {};
        if (name !== undefined) updateData.name = name;
        if (quantity !== undefined) updateData.quantity = quantity;
        if (ingredients !== undefined) {
            // Validate ingredients array structure
            if (!Array.isArray(ingredients)) {
                return NextResponse.json({ 
                    error: 'Ingredients must be an array' 
                }, { status: 400 });
            }
            
            // Validate each ingredient in the array
            for (const ingredient of ingredients) {
                if (!ingredient.ingredient_id || ingredient.deduct_amount === undefined || ingredient.deduct_stock === undefined) {
                    return NextResponse.json({ 
                        error: 'Each ingredient must have ingredient_id, deduct_amount, and deduct_stock' 
                    }, { status: 400 });
                }
            }
            updateData.ingredients = ingredients;
        }
        if (type !== undefined) updateData.type = type;
        if (stock !== undefined) updateData.stock = stock;

        const { data, error } = await supabaseServerClient
            .from('recipes')
            .update(updateData)
            .eq('id', id)
            .select();

        if (error) {
            throw error;
        }

        return NextResponse.json(data[0], { status: 200 });
    } catch (error: any) {
        console.error('Error updating recipe:', error.message);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
};

export const DELETE = async (req: Request) => {
    try {
        const body = await req.json();
        const { id } = body;

        if (!id) {
            return NextResponse.json({ error: 'Recipe ID is required' }, { status: 400 });
        }

        const { data, error } = await supabaseServerClient
            .from('recipes')
            .delete()
            .eq('id', id);

        if (error) {
            throw error;
        }

        return NextResponse.json({ message: 'Recipe deleted successfully' }, { status: 200 });
    } catch (error: any) {
        console.error('Error deleting recipe:', error.message);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
};
