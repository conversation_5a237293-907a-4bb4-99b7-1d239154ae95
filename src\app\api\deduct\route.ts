import { NextResponse } from 'next/server';
import { supabase as supabaseServerClient } from '@/lib/supabaseClient';

// Deduct stock from ingredients based on new schema
export const POST = async (req: Request) => {
    try {
        const body = await req.json();
        const { type, id, quantity } = body;

        if (!type || !id || quantity === undefined) {
            return NextResponse.json({ 
                error: 'Type, id, and quantity are required' 
            }, { status: 400 });
        }

        if (type === 'ingredient') {
            // Deduct from ingredients table
            const { data: ingredient, error: fetchError } = await supabaseServerClient
                .from('ingredients')
                .select('*')
                .eq('id', id)
                .single();

            if (fetchError) {
                throw fetchError;
            }

            if (!ingredient) {
                return NextResponse.json({ error: 'Ingredient not found' }, { status: 404 });
            }

            if (ingredient.stock < quantity) {
                return NextResponse.json({ 
                    error: `Insufficient stock. Available: ${ingredient.stock}, Required: ${quantity}` 
                }, { status: 400 });
            }

            const newStock = ingredient.stock - quantity;

            const { data, error } = await supabaseServerClient
                .from('ingredients')
                .update({ stock: newStock })
                .eq('id', id)
                .select();

            if (error) {
                throw error;
            }

            return NextResponse.json({ 
                message: 'Ingredient stock deducted successfully',
                data: data[0],
                deducted: quantity,
                newStock
            }, { status: 200 });

        } else if (type === 'recipe') {
            // Deduct from recipes table and cascade to ingredients
            const { data: recipe, error: fetchError } = await supabaseServerClient
                .from('recipes')
                .select('*')
                .eq('id', id)
                .single();

            if (fetchError) {
                throw fetchError;
            }

            if (!recipe) {
                return NextResponse.json({ error: 'Recipe not found' }, { status: 404 });
            }

            if (recipe.stock < quantity) {
                return NextResponse.json({ 
                    error: `Insufficient recipe stock. Available: ${recipe.stock}, Required: ${quantity}` 
                }, { status: 400 });
            }

            // Deduct from recipe stock
            const newRecipeStock = recipe.stock - quantity;

            const { data: updatedRecipe, error: recipeError } = await supabaseServerClient
                .from('recipes')
                .update({ stock: newRecipeStock })
                .eq('id', id)
                .select();

            if (recipeError) {
                throw recipeError;
            }

            // Deduct from recipe ingredients
            const deductionResults = [];
            
            if (recipe.ingredients && Array.isArray(recipe.ingredients)) {
                for (const recipeIngredient of recipe.ingredients) {
                    const { ingredient_id, deduct_amount, deduct_stock } = recipeIngredient;
                    
                    // Calculate total deduction needed
                    const totalDeductAmount = deduct_amount * quantity;
                    const totalDeductStock = deduct_stock * quantity;

                    // Get current ingredient
                    const { data: ingredient, error: ingredientFetchError } = await supabaseServerClient
                        .from('ingredients')
                        .select('*')
                        .eq('id', ingredient_id)
                        .single();

                    if (ingredientFetchError || !ingredient) {
                        console.warn(`Ingredient ${ingredient_id} not found, skipping deduction`);
                        continue;
                    }

                    // Check if there's enough stock
                    if (ingredient.stock < totalDeductStock) {
                        return NextResponse.json({ 
                            error: `Insufficient stock for ingredient ${ingredient.name}. Available: ${ingredient.stock}, Required: ${totalDeductStock}` 
                        }, { status: 400 });
                    }

                    // Deduct from ingredient stock
                    const newIngredientStock = ingredient.stock - totalDeductStock;

                    const { data: updatedIngredient, error: ingredientUpdateError } = await supabaseServerClient
                        .from('ingredients')
                        .update({ stock: newIngredientStock })
                        .eq('id', ingredient_id)
                        .select();

                    if (ingredientUpdateError) {
                        throw ingredientUpdateError;
                    }

                    deductionResults.push({
                        ingredient_id,
                        ingredient_name: ingredient.name,
                        deducted_amount: totalDeductAmount,
                        deducted_stock: totalDeductStock,
                        new_stock: newIngredientStock
                    });
                }
            }

            return NextResponse.json({ 
                message: 'Recipe stock deducted successfully',
                recipe: updatedRecipe[0],
                ingredient_deductions: deductionResults,
                deducted: quantity,
                newRecipeStock
            }, { status: 200 });

        } else {
            return NextResponse.json({ error: 'Invalid type. Must be "ingredient" or "recipe"' }, { status: 400 });
        }

    } catch (error: any) {
        console.error('Error in deduction:', error.message);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
};
