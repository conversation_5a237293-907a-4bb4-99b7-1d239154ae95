import { NextResponse } from 'next/server';
import { supabase as supabaseServerClient } from '@/lib/supabaseClient';

export const GET = async () => {
    try {
        const { data, error } = await supabaseServerClient
            .from('ingredients')
            .select('*')
            .order('id', { ascending: true });
        
        if (error) {
            throw error;
        }

        return NextResponse.json(data, { status: 200 });
    } catch (error: any) {
        console.error('Error fetching ingredients:', error.message);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
};

export const POST = async (req: Request) => {
    try {
        const body = await req.json();
        const { name, unit, quantity, stock, is_liquid } = body;

        if (!name || !unit || quantity === undefined || stock === undefined) {
            return NextResponse.json({ 
                error: 'Name, unit, quantity, and stock are required' 
            }, { status: 400 });
        }

        const { data, error } = await supabaseServerClient
            .from('ingredients')
            .insert({
                name,
                unit,
                quantity,
                stock,
                is_liquid: is_liquid || false
            })
            .select();

        if (error) {
            throw error;
        }

        return NextResponse.json(data[0], { status: 201 });
    } catch (error: any) {
        console.error('Error creating ingredient:', error.message);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
};

export const PUT = async (req: Request) => {
    try {
        const body = await req.json();
        const { id, name, unit, quantity, stock, is_liquid } = body;

        if (!id) {
            return NextResponse.json({ error: 'Ingredient ID is required' }, { status: 400 });
        }

        const updateData: any = {};
        if (name !== undefined) updateData.name = name;
        if (unit !== undefined) updateData.unit = unit;
        if (quantity !== undefined) updateData.quantity = quantity;
        if (stock !== undefined) updateData.stock = stock;
        if (is_liquid !== undefined) updateData.is_liquid = is_liquid;

        const { data, error } = await supabaseServerClient
            .from('ingredients')
            .update(updateData)
            .eq('id', id)
            .select();

        if (error) {
            throw error;
        }

        return NextResponse.json(data[0], { status: 200 });
    } catch (error: any) {
        console.error('Error updating ingredient:', error.message);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
};

export const DELETE = async (req: Request) => {
    try {
        const body = await req.json();
        const { id } = body;

        if (!id) {
            return NextResponse.json({ error: 'Ingredient ID is required' }, { status: 400 });
        }

        const { data, error } = await supabaseServerClient
            .from('ingredients')
            .delete()
            .eq('id', id);

        if (error) {
            throw error;
        }

        return NextResponse.json({ message: 'Ingredient deleted successfully' }, { status: 200 });
    } catch (error: any) {
        console.error('Error deleting ingredient:', error.message);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
};
